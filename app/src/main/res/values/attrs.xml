<?xml version="1.0" encoding="utf-8"?>
<resources>
    <attr name="colorAccent" format="color" />
    <!--  activity / fragment 主layout使用  -->
    <attr name="mainLayoutBg" format="color" /> <!--cfffffff_c1a1d20-->
    <!--  Brand Colors Primary 品牌色 -->
    <attr name="color_c034854_ce35728" format="color" />
    <!--  background cards 背景 和 卡片 -->
    <attr name="color_cf5f5f5_c1a1d20" format="color" />
    <attr name="color_cffffff_c262930" format="color" />
    <attr name="color_c262930_cffffff" format="color" />

    <!--  text 文字 -->
    <attr name="color_c1e1e1e_cebffffff" format="color" />
    <attr name="color_ca61e1e1e_c99ffffff" format="color" />
    <attr name="color_c731e1e1e_c61ffffff" format="color" />
    <attr name="color_c1f1e1e1e_c1fffffff" format="color" />
    <attr name="color_cebffffff_c1e1e1e" format="color" />
    <!--  Field 输入框  -->
    <attr name="color_c0a1e1e1e_c262930" format="color" />
    <!--  分割线 & Order按钮  -->
    <attr name="color_c0a1e1e1e_c0affffff" format="color" />
    <!--  frame 边框  -->
    <attr name="color_c331e1e1e_c33ffffff" format="color" />
    <!-- Profile Verification Labels 配置文件验证标签 图标色值 -->
    <attr name="color_ce8e8e8_c414348" format="color" />
    <attr name="color_cfcebe5_c3e3535" format="color" />
    <attr name="color_ce5f7f3_c283b3d" format="color" />
    <attr name="color_cf9ebe6_c3b2f2f" format="color" />
    <attr name="color_cfae9e8_c3c2d32" format="color" />
    <attr name="color_ce0f0ff_c0c2c4d" format="color" />
    <!--  Share Background 分享卡片 / K线分享背景  -->
    <attr name="color_cf3f3f3_c262930" format="color" />
    <attr name="color_ce4e4e4_c16181b" format="color" />
    <!--  WhatsApp Buttons WhatsApp 按钮  -->
    <attr name="color_cbf25d366_c3325d366" format="color" />
    <!-- Grabber 下拉滑块 -->
    <attr name="color_c4d1e1e1e_c4dffffff" format="color" />
    <!-- VolSeekBar -->
    <attr name="color_cffffff_c1a1d20" format="color" />
    <attr name="color_ff1e1e1e_ebffffff" format="color" />
    <attr name="color_0a1e1e1e_0affffff" format="color" />
    <!-- Trades新手引导 -->
    <attr name="color_c1f1e1e1e_c262930" format="color" />
    <!-- 分享切换使用 -->
    <attr name="color_c0a1e1e1e_c0ab2b2b2" format="color" />

    <attr name="color_c0a1e1e1e_c1fffffff" format="color" />
    <attr name="color_cffffff_cebffffff" format="color" />
    <attr name="color_c1e1e1e_cffffff" format="color" />


    <attr name="mainTabTrades" format="reference" />
    <attr name="mainTabHome" format="reference" />
    <attr name="mainTabDiscover" format="reference" />
    <attr name="mainTabPromo" format="reference" />
    <attr name="mainTabProfile" format="reference" />
    <attr name="mainTabClose" format="reference" />
    <attr name="icon1Msg" format="reference" />
    <attr name="icon1Cs" format="reference" />
    <attr name="icon1Share" format="reference" />
    <attr name="icon1Share2" format="reference" />
    <attr name="icon1Menu" format="reference" />
    <attr name="icon1Info" format="reference" />
    <attr name="icon1Setting" format="reference" />
    <attr name="icon1OrderSetting" format="reference" />
    <attr name="icon1Drawing" format="reference" />
    <attr name="icon1Guide" format="reference" />
    <attr name="icon1Faq" format="reference" />
    <attr name="icon1Signals" format="reference" />
    <attr name="icon1Refresh" format="reference" />
    <attr name="icon1AlertPrice" format="reference" />
    <attr name="icon1SwitchAccount" format="reference" />
    <attr name="icon1NoticeClear" format="reference" />
    <attr name="icon1WalletHistory" format="reference" />
    <attr name="icon2CloseCircle" format="reference" />
    <attr name="icon2ECInactive" format="reference" />
    <attr name="icon2ECOut" format="reference" />
    <attr name="icon2Edit12x12" format="reference" />
    <attr name="icon2Edit16x16" format="reference" />
    <attr name="icon2EditStrategyFunds" format="reference" />
    <attr name="icon2NextInactive" format="reference" />
    <attr name="icon2ProfileUidCopy" format="reference" />
    <attr name="icon2Sub" format="reference" />
    <attr name="icon2SymbolSwitch" format="reference" />
    <attr name="icon2Delete" format="reference" />
    <attr name="icon2EditTpSl" format="reference" />
    <attr name="icon2CloseCircle12x12" format="reference" />
    <attr name="icon2HistoryEnter" format="reference" />
    <attr name="icon2SharePosition" format="reference" />
    <attr name="icon2ReservePosition" format="reference" />
    <attr name="icon2ArrowBottom" format="reference" />
    <attr name="icon2BgSellUnselected" format="reference" />
    <attr name="icon2BgSellNoTrading" format="reference" />
    <attr name="icon2BgBuyUnselected" format="reference" />
    <attr name="icon2BgBuyNoTrading" format="reference" />
    <attr name="icon2CbSquareUncheck" format="reference" />

    <attr name="imgLogo" format="reference" />
    <attr name="imgLogoMark" format="reference" />
    <attr name="imgProfileWelcome" format="reference" />
    <attr name="imgProfileDeposit" format="reference" />
    <attr name="imgProfileTransfer" format="reference" />
    <attr name="imgProfileWithdraw" format="reference" />
    <attr name="imgProfileFunds" format="reference" />
    <attr name="imgProfileCryptoWallet" format="reference" />
    <attr name="imgProfileVantageRewards" format="reference" />
    <attr name="imgProfileMissionCenter" format="reference" />
    <attr name="imgProfileCoupons" format="reference" />
    <attr name="imgProfileReferrals" format="reference" />
    <attr name="imgProfileFavourites" format="reference" />
    <attr name="imgProfileIb" format="reference" />
    <attr name="imgProfilePriceAlter" format="reference" />
    <attr name="imgProfileSecurity" format="reference" />
    <attr name="imgProfileTransferIB" format="reference" />
    <attr name="imgProfileSettings" format="reference" />
    <attr name="imgAlertOk" format="reference" />
    <attr name="imgAlertWrong" format="reference" />
    <attr name="imgAlertProcessed" format="reference" />
    <attr name="imgAcademyRightTop" format="reference" />
    <attr name="imgAcademyRightBottom" format="reference" />
    <attr name="imgHelpFaqs" format="reference" />
    <attr name="imgHelpContactUs" format="reference" />
    <attr name="imgSecurityPasskeyIcon" format="reference" />
    <attr name="imgSecurityPasskeyAuth" format="reference" />
    <attr name="imgSecurityPasskeyCreating" format="reference" />
    <attr name="imgSecurityPasskeyCreatingFailed" format="reference" />
    <attr name="imgNoDataBase" format="reference" />
    <attr name="img2FALink" format="reference" />
    <attr name="imgAlertFail" format="reference" />
    <attr name="imgOpenLiveGuide1" format="reference" />
    <attr name="imgOpenLiveGuide2" format="reference" />
    <attr name="imgOpenLiveGuide3" format="reference" />
    <attr name="imgOpenLiveGuide4" format="reference" />
    <attr name="imgTheme" format="reference" />
    <attr name="imgUnlockTop" format="reference" />
    <attr name="imgUnlockOpen" format="reference" />
    <attr name="imgUnlockTrades" format="reference" />
    <attr name="imgUnlockFingerprint" format="reference" />
    <attr name="imgOpenStepAccount" format="reference" />
    <attr name="imgOpenStepPersonal" format="reference" />
    <attr name="imgOpenStepDeclaration" format="reference" />
    <attr name="imgOpenStepID" format="reference" />
    <attr name="imgOpenStepPOA" format="reference" />
    <attr name="imgOpenStepUnSelectedAccount" format="reference" />
    <attr name="imgOpenStepUnSelectedPersonal" format="reference" />
    <attr name="imgOpenStepUnSelectedDeclaration" format="reference" />
    <attr name="imgOpenStepUnSelectedID" format="reference" />
    <attr name="imgOpenStepUnSelectedPOA" format="reference" />
    <attr name="imgPermissionWithdrawal" format="reference" />
    <attr name="imgDepositCreditCard" format="reference" />
    <attr name="imgCardNumber" format="reference" />
    <attr name="imgTradingViewDrawingBrush" format="reference" />
    <attr name="imgTradingViewDrawingHighLighter" format="reference" />
    <attr name="imgTradingViewDrawingEraser" format="reference" />
    <attr name="imgTradingViewLineTrend" format="reference" />
    <attr name="imgTradingViewLineHorizontal" format="reference" />
    <attr name="imgTradingViewLineVertical" format="reference" />
    <attr name="icon2KlineScreenExpand" format="reference" />
    <!--新版k线绘图icon START-->
    <attr name="imgKlineLogo" format="reference" />
    <attr name="icon2KlineDrawLineHorizontalSelect" format="reference" />
    <attr name="icon2KlineDrawLineVerticalSelect" format="reference" />
    <attr name="icon2KlineDrawShapeBezierSelect" format="reference" />
    <attr name="icon2KlineDrawShapeRectangleSelect" format="reference" />
    <attr name="icon2KlineDrawTrendLineSelect" format="reference" />
    <attr name="icon2KlineDrawRaysSelect" format="reference" />
    <attr name="icon2KlineDrawParallelLineSelect" format="reference" />
    <attr name="icon2KlineDrawToolsContinueSelect" format="reference" />
    <attr name="icon2KlineDrawToolsShowSelect" format="reference" />
    <attr name="icon2KlineDrawToolsClean" format="reference" />
    <attr name="icon2KlineDrawToolsMain" format="reference" />
    <attr name="icon2KlineDrawToolsDraw" format="reference" />
    <!--    <attr name="colorKlineParamsMainSubSelect" format="reference" />-->
    <attr name="icon2KlineDrawToolsHandle" format="reference" />
    <attr name="icon2KlineDrawToolsEdit" format="reference" />
    <attr name="icon2KlineDrawToolsSolid1" format="reference" />
    <attr name="icon2KlineDrawToolsSolid2" format="reference" />
    <attr name="icon2KlineDrawToolsSolid3" format="reference" />
    <attr name="icon2KlineDrawToolsDelete" format="reference" />
    <attr name="icon2KlineDrawToolsArrowDown" format="reference" />
    <attr name="icon2KlineDrawToolsArrowUp" format="reference" />
    <!--新版k线绘图icon END-->
    <attr name="imgTradingViewLinePath" format="reference" />
    <attr name="imgTradingViewShapeRectangle" format="reference" />
    <attr name="imgTradingViewShapePolyline" format="reference" />
    <attr name="imgAddBank" format="reference" />
    <attr name="imgShareEdit" format="reference" />
    <attr name="imgShareCopyLink" format="reference" />
    <attr name="imgShareMore" format="reference" />
    <attr name="imgShareSave" format="reference" />
    <attr name="imgShareThemeLogo" format="reference" />
    <attr name="imgShareThemeLogoSelect" format="reference" />
    <attr name="imgShareThemeFerrari" format="reference" />
    <attr name="imgShareThemeFerrariSelect" format="reference" />
    <attr name="imgShareLogo" format="reference" />
    <attr name="imgBankStatement" format="reference" />
    <attr name="imgDriversLicense" format="reference" />
    <attr name="imgPassport" format="reference" />
    <attr name="imgIdentificationCard" format="reference" />
    <attr name="imgUtilityBills" format="reference" />
    <attr name="imgLetter" format="reference" />
    <attr name="imgFollower" format="reference" />
    <attr name="imgPermissionWithdrawalFailed" format="reference" />
    <attr name="img2faPhoneOtpSelect" format="reference" />
    <attr name="img2faPhoneOtpUnselect" format="reference" />
    <attr name="img2faSelect" format="reference" />
    <attr name="img2faUnselect" format="reference" />
    <attr name="img2faPwdSelect" format="reference" />
    <attr name="img2faPwdUnselect" format="reference" />
    <attr name="img2faEmailOtpSelect" format="reference" />
    <attr name="img2faEmailOtpUnselect" format="reference" />
    <attr name="imgNoticeTrade" format="reference" />
    <attr name="imgNoticeCopyTrading" format="reference" />
    <attr name="imgNoticePriceAlerts" format="reference" />
    <attr name="imgNoticeAccount" format="reference" />
    <attr name="imgNoticeAnnouncements" format="reference" />
    <attr name="imgNoticeNews" format="reference" />
    <attr name="imgCircleRight" format="reference" />
    <attr name="imgTelegramNotLinked" format="reference" />
    <attr name="imgInApp" format="reference" />
    <attr name="imgAskUnselected" format="reference" />
    <attr name="imgClassicUnselected" format="reference" />
    <attr name="imgNotSort" format="reference" />
    <attr name="imgUpSort" format="reference" />
    <attr name="imgDownSort" format="reference" />
    <attr name="imgTradesGuideTop" format="reference" />
    <attr name="imgTradeNeverLogin" format="reference" />
    <attr name="imgTradeNotLogin" format="reference" />
    <attr name="imgReverseOrder" format="reference" />
    <attr name="imgItemChecked" format="reference" />
    <attr name="imgVerifyLv1" format="reference" />
    <attr name="imgVerifyLv2" format="reference" />
    <attr name="imgVerifyLv3" format="reference" />
    <attr name="imgUnVerification" format="reference" />
    <attr name="imgWelcomeDeposit" format="reference" />
    <attr name="imgWelcomeWithdraw" format="reference" />
    <attr name="imgWelcomeTrades" format="reference" />
    <attr name="imgFeatureLock" format="reference" />
    <attr name="imgFeatureWallet" format="reference" />
    <attr name="imgNotReceiveCodeTips1" format="reference" />
    <attr name="imgNotReceiveCodeTips2" format="reference" />
    <attr name="imgNotReceiveCodeTips3" format="reference" />
    <attr name="imgNotReceiveCodeTips4" format="reference" />
    <attr name="bgMt4Confirm" format="reference" />
    <attr name="icNoDataBase" format="reference" />
    <attr name="bgServerMaintenance" format="reference" />
    <attr name="imgPriceAlertMiss" format="reference" />
    <attr name="icNoConnection" format="reference" />
    <!-- 选择日期时间 -->
    <declare-styleable name="PickerView">
        <attr name="isLoop" format="boolean" />
        <attr name="commonStyle" format="integer" />
    </declare-styleable>
    <declare-styleable name="PasswordView">
        <attr name="passwordLength" format="integer" />
        <attr name="passwordPadding" format="dimension" />
        <attr name="borderColor" format="color" />
        <attr name="passwordColor" format="color" />
        <attr name="borderWidth" format="dimension" />
        <attr name="cursorWidth" format="dimension" />
        <attr name="cursorFlashTime" format="integer" />
        <attr name="isCursorEnable" format="boolean" />
        <attr name="cipherEnable" format="boolean" />
        <attr name="cursorColor" format="color" />
        <attr name="rectFillColor" format="color" />
        <attr name="rectIsFill" format="boolean" />
        <attr name="rectRound" format="dimension" />
        <attr name="mode" format="enum">
            <enum name="underline" value="0" />
            <enum name="rect" value="1" />
        </attr>
    </declare-styleable>
    <declare-styleable name="RoundedBarChart">
        <attr name="radius" format="dimension" />
    </declare-styleable>
    <!--背景图片-->
    <attr name="src" format="reference" />
    <!--遮罩层颜色，建议带透明度-->
    <attr name="maskLayerColor" format="color" />
    <!--是否滚动-->
    <attr name="isScroll" format="boolean" />
    <!--滚动速度,建议取值区间 [1,50] -->
    <attr name="speed" format="integer">
        <enum name="slow" value="1" />
        <enum name="ordinary" value="3" />
        <enum name="fast" value="5" />
    </attr>
    <!-- 滚动方向,默认是竖直方向滚动-->
    <attr name="scrollOrientation" format="integer">
        <enum name="toTop" value="0" />
        <enum name="toBottom" value="1" />
        <enum name="toLeft" value="2" />
        <enum name="toRight" value="3" />
    </attr>
    <declare-styleable name="SrcScrollFrameLayout">
        <attr name="src" />
        <attr name="maskLayerColor" />
        <attr name="isScroll" />
        <attr name="speed" />
        <attr name="scrollOrientation" />
    </declare-styleable>
    <declare-styleable name="SrcLoopScrollFrameLayout">
        <attr name="src" />
        <attr name="maskLayerColor" />
        <attr name="isScroll" />
        <attr name="speed" />
        <attr name="scrollOrientation" />
    </declare-styleable>
    <!-- 飘心动画自定义的属性 -->
    <declare-styleable name="HeartLayout">
        <attr name="initX" format="dimension" />
        <attr name="initY" format="dimension" />
        <attr name="xRand" format="dimension" />
        <attr name="animLengthRand" format="dimension" />
        <attr name="xPointFactor" format="dimension" />
        <attr name="animLength" format="dimension" />
        <attr name="heart_width" format="dimension" />
        <attr name="heart_height" format="dimension" />
        <attr name="bezierFactor" format="integer" />
        <attr name="anim_duration" format="integer" />
    </declare-styleable>
    <declare-styleable name="BarChart">
        <attr name="arrow_width" format="dimension" />
        <attr name="arrow_height" format="dimension" />
        <attr name="arrow_round" format="dimension" />
        <attr name="back_color" format="color" />
    </declare-styleable>
    <declare-styleable name="BubbleSeekBar">
        <attr name="bsb_min" format="float|reference" /> <!--min < max, default: 0.0f-->
        <attr name="bsb_max" format="float|reference" /> <!--min < max, default: 100.0f-->
        <attr name="bsb_progress" format="float|reference" /> <!--real time progress value, default: min-->
        <attr name="bsb_is_float_type" format="boolean" /> <!--support for float type-->
        <attr name="bsb_track_size" format="dimension|reference" /> <!--height of right-track(on the right of thumb), default: 2dp-->
        <!--height of left-track(on the left of thumb), default: 2dp higher than right-track's height-->
        <attr name="bsb_second_track_size" format="dimension|reference" />
        <attr name="bsb_thumb_radius" format="dimension|reference" /> <!--radius of thumb, default: 2dp higher than left-track's height-->
        <!--radius of thumb when be dragging, default: 2 times of left-track's height-->
        <attr name="bsb_thumb_radius_on_dragging" format="dimension|reference" />
        <attr name="bsb_track_color" format="color|reference" /> <!--color of right-track, default: R.color.colorPrimary-->
        <attr name="bsb_second_track_color" format="color|reference" /> <!--color of left-track, default: R.color.colorAccent-->
        <attr name="bsb_thumb_color" format="color|reference" /> <!--color of thumb, default: same as left-track's color-->
        <attr name="bsb_section_count" format="integer|reference" /> <!--shares of whole progress(max - min), default: 10-->
        <attr name="bsb_show_section_mark" format="boolean" /> <!--show demarcation points or not, default: false-->
        <attr name="bsb_auto_adjust_section_mark" format="boolean" /> <!--auto scroll to the nearest section_mark or not, default: false-->
        <attr name="bsb_show_section_text" format="boolean" /> <!--show section-text or not, default: false-->
        <attr name="bsb_section_text_size" format="dimension|reference" /> <!--text size of section-text, default: 14sp-->
        <attr name="bsb_section_text_color" format="color|reference" /> <!--text color of section-text, default: same as right-track's color-->
        <!--text position of section-text relative to track, sides, bottom_sides, below_section_mark, default: sides-->
        <attr name="bsb_section_text_position">
            <enum name="sides" value="0" />
            <enum name="bottom_sides" value="1" />
            <enum name="below_section_mark" value="2" />
        </attr>
        <attr name="bsb_section_text_interval" format="integer" /> <!--the interval of two section-text, default: 1-->
        <attr name="bsb_show_thumb_text" format="boolean" /> <!--show real time progress-text under thumb or not, default: false-->
        <attr name="bsb_thumb_text_size" format="dimension|reference" /> <!--text size of progress-text, default: 14sp-->
        <attr name="bsb_thumb_text_color" format="color|reference" /> <!--text color of progress-text, default: same as left-track's color-->
        <attr name="bsb_show_progress_in_float" format="boolean" /> <!--show bubble-progress in float or not, default: false-->
        <attr name="bsb_touch_to_seek" format="boolean" /> <!--touch anywhere on track to quickly seek, default: false-->
        <attr name="bsb_seek_step_section" format="boolean" /> <!--seek one step by one section, the progress is discrete, default: false-->
        <attr name="bsb_seek_by_section" format="boolean" /> <!--seek by section, the progress may not be linear, default: false-->
        <attr name="bsb_bubble_color" format="color|reference" /> <!--color of bubble, default: same as left-track's color-->
        <attr name="bsb_bubble_text_size" format="dimension|reference" /> <!--text size of bubble-progress, default: 14sp-->
        <attr name="bsb_bubble_text_color" format="color|reference" /> <!--text color of bubble-progress, default: #ffffffff-->
        <attr name="bsb_anim_duration" format="integer" /> <!--duration of animation, default: 200ms-->
        <attr name="bsb_always_show_bubble" format="boolean" /> <!--bubble shows all time, default: false-->
        <attr name="bsb_always_show_bubble_delay" format="integer" /> <!--the delay duration before bubble shows all the time, default: 200ms-->
        <attr name="bsb_hide_bubble" format="boolean" /> <!--hide bubble, default: false-->
        <attr name="bsb_rtl" format="boolean" /> <!--right to left, default: false-->
        <attr name="android:enabled" />
    </declare-styleable>
    <declare-styleable name="CustomSeekBar">
        <attr name="seek_min" format="integer|reference" /> <!--min < max, default: 0-->
        <attr name="seek_max" format="integer|reference" /> <!--min < max, default: 100-->
        <attr name="seek_progress" format="integer|reference" /> <!--real time progress value, default: min-->
        <attr name="seek_progress_drawable" format="reference" />
        <attr name="seek_thumb" format="reference" />
        <attr name="most_color" format="reference|color" />
    </declare-styleable>
    <declare-styleable name="BoxShadowLayout">
        <!-- box-shadow attributes-->
        <!-- 水平阴影的位置。允许负值。 -->
        <attr name="box_shadowOffsetHorizontal" format="dimension" />
        <!-- 垂直阴影的位置。允许负值。 -->
        <attr name="box_shadowOffsetVertical" format="dimension" />
        <!-- 模糊距离 -->
        <attr name="box_shadowBlur" format="dimension" />
        <!-- 阴影的尺寸 -->
        <attr name="box_shadowSpread" format="dimension" />
        <!-- 阴影的颜色 -->
        <attr name="box_shadowColor" format="color|reference" />
        <!-- 将外部阴影 (outset) 改为内部阴影 -->
        <attr name="box_shadowInset" format="boolean" />
        <!-- extended attributes-->
        <!-- 圆角大小 -->
        <attr name="box_radius" format="dimension" />
        <attr name="box_radiusTopLeft" format="dimension" />
        <attr name="box_radiusTopRight" format="dimension" />
        <attr name="box_radiusBottomLeft" format="dimension" />
        <attr name="box_radiusBottomRight" format="dimension" />
    </declare-styleable>
    <!-- 垂直方向的虚线 -->
    <declare-styleable name="DividerView">
        <!-- 虚线颜色 -->
        <attr name="divider_line_color" format="color" />
        <!-- 虚线宽度 -->
        <attr name="dashThickness" format="dimension" />
        <!-- 虚线dash宽度 -->
        <attr name="dashLength" format="dimension" />
        <!-- 虚线dash间隔 -->
        <attr name="dashGap" format="dimension" />
        <!-- 虚线朝向 -->
        <attr name="divider_orientation" format="enum">
            <enum name="horizontal" value="0" />
            <enum name="vertical" value="1" />
        </attr>
    </declare-styleable>
    <!--设置项Item -->
    <declare-styleable name="SettingItemView">
        <!--左边标题-->
        <attr name="item_title" format="reference|string" />
        <!-- title 字体大小-->
        <attr name="item_title_size" format="dimension|reference" />
        <!-- title 竖着方向margin，撑起布局-->
        <attr name="item_title_margin_vertical" format="dimension|reference" />
        <!--右边文案-->
        <attr name="item_detail" format="reference|string" />
        <attr name="item_detail_text_size" format="dimension|reference" />
        <attr name="item_detail_text_color" format="color" />
        <!--右边Icon-->
        <attr name="item_right_status_icon" format="reference" />
        <!--是否显示底部分隔横线-->
        <attr name="item_line_show" format="boolean" />
        <!--是否显示右侧箭头-->
        <attr name="item_show_right_arrow" format="boolean" />
    </declare-styleable>
    <declare-styleable name="HeaderBar">
        <attr name="hb_isShowBack" format="boolean" />
        <attr name="hb_titleText" format="string" />
        <attr name="hb_endIcon" format="reference" />
        <attr name="hb_endIcon1" format="reference" />
        <attr name="hb_endIcon2" format="reference" />
        <attr name="hb_endIcon3" format="reference" />
        <attr name="hb_endIconSize" format="dimension" />
        <attr name="hb_endText" format="string" />
        <!--点击返回按键自动关闭Activity，true不会自动关闭，false默认会自动关闭-->
        <attr name="hb_backClickAutoFinishDisallow" format="boolean" />
    </declare-styleable>
    <declare-styleable name="HintMaintenanceView">
        <attr name="maintenance_view_title" format="string" />
        <attr name="maintenance_view_content" format="string" />
        <attr name="maintenance_view_time" format="string" />
    </declare-styleable>
    <!-- NoDataView自定义属性 -->
    <declare-styleable name="NoDataView">
        <attr name="ndv_hintMessage" format="string" />
        <attr name="ndv_icon" format="reference" />
        <!--下方按钮text文本-->
        <attr name="ndv_bottomButtonText" format="string" />
    </declare-styleable>
    <!-- NoDataScrollView自定义属性 -->
    <declare-styleable name="NoDataScrollView">
        <attr name="ndsv_hintMessage" format="string" />
        <attr name="ndsv_icon" format="reference" />
        <!--下方按钮text文本-->
        <attr name="ndsv_bottomButtonText" format="string" />
    </declare-styleable>
    <!-- NoDataScrollView自定义属性 -->
    <declare-styleable name="VolSeekBar">
        <!-- 进度 -->
        <!--最小值，当为0时，显示最小值-->
        <attr name="vsb_min" format="integer|reference" />
        <!--最大值-->
        <attr name="vsb_max" format="integer|reference" />
        <!--进度值-->
        <attr name="vsb_progress" format="integer|reference" />
        <!-- 轨道 track -->
        <!--背景颜色-->
        <attr name="vsb_track_backgroundColor" format="color|reference" />
        <!--前景颜色-->
        <attr name="vsb_track_progressColor" format="color|reference" />
        <!--轨道宽度-->
        <attr name="vsb_track_width" format="dimension|reference" />
        <!-- 平分进度的小圆点 -->
        <!--是否显示小圆点-->
        <attr name="vsb_dot_isShow" format="boolean|reference" />
        <!--小圆点数量-->
        <attr name="vsb_dot_count" format="integer|reference" />
        <!--小圆点半径-->
        <attr name="vsb_dot_radius" format="dimension|reference" />
        <!--小圆点边框宽度-->
        <attr name="vsb_dot_strokeWidth" format="dimension|reference" />
        <!--小圆点边框颜色-->
        <attr name="vsb_dot_strokeColor" format="color|reference" />
        <!--小圆点背景色-->
        <attr name="vsb_dot_solidBackColor" format="color|reference" />
        <!--是否需要震动-->
        <attr name="vsb_isNeedVibrate" format="boolean|reference" />
        <!-- 滑块 -->
        <!--滑块半径-->
        <attr name="vsb_thumb_radius" format="dimension|reference" />
        <!--滑块填充色-->
        <attr name="vsb_thumb_solidColor" format="color|reference" />
        <!--滑块边大小-->
        <attr name="vsb_thumb_strokeWidth" format="dimension|reference" />
        <!-- 底部百分比文字 -->
        <!--是否显示底部百分比数字-->
        <attr name="vsb_percent_isShow" format="boolean|reference" />
        <!--底部百分比数字文字大小-->
        <attr name="vsb_percent_textSize" format="dimension|reference" />
        <!--底部百分比数字文字颜色-->
        <attr name="vsb_percent_textColor" format="color|reference" />
        <!--底部百分比单位-->
        <attr name="vsb_percent_unit" format="string|reference" />
        <!--底部百分比数字距滑块的间距-->
        <attr name="vsb_percent_margin" format="dimension|reference" />
        <!-- 气泡 -->
        <!--是否显示气泡-->
        <attr name="vsb_bubble_isShow" format="boolean|reference" />
        <!--气泡高度-->
        <attr name="vsb_bubble_height" format="dimension|reference" />
        <!--气泡圆角-->
        <attr name="vsb_bubble_radius" format="dimension|reference" />
        <!--气泡与滑块的间距-->
        <attr name="vsb_bubble_margin" format="dimension|reference" />
        <!--气泡横向内间距-->
        <attr name="vsb_bubble_horizontalPadding" format="dimension|reference" />
        <!--气泡文字大小-->
        <attr name="vsb_bubble_textSize" format="dimension|reference" />
        <!--气泡文字颜色-->
        <attr name="vsb_bubble_textColor" format="dimension|reference" />
        <!--气泡的背景色-->
        <attr name="vsb_bubble_color" format="color|reference" />
    </declare-styleable>
    <declare-styleable name="ExpandableTextView">
        <!--保留的行数-->
        <attr name="ep_max_line" format="integer" />
        <!--是否需要展开-->
        <attr name="ep_need_expand" format="boolean" />
        <!--是否需要收起 这个是建立在开启展开的基础上的-->
        <attr name="ep_need_contract" format="boolean" />
        <!--是否需要@用户 -->
        <attr name="ep_need_mention" format="boolean" />
        <!--是否需要对链接进行处理 -->
        <attr name="ep_need_link" format="boolean" />
        <!--是否需要动画-->
        <attr name="ep_need_animation" format="boolean" />
        <!--是否需要永远将展开或者收回放置在最后边-->
        <attr name="ep_need_always_showright" format="boolean" />
        <!--是否需要将连接转换成网页链接显示 默认为true-->
        <attr name="ep_need_convert_url" format="boolean" />
        <!--是否需要自定义规则-->
        <attr name="ep_need_self" format="boolean" />
        <!--收起的文案-->
        <attr name="ep_contract_text" format="string" />
        <!--展开的文案-->
        <attr name="ep_expand_text" format="string" />
        <!--展开的文字的颜色-->
        <attr name="ep_expand_color" format="color" />
        <!--收起的文字的颜色-->
        <attr name="ep_contract_color" format="color" />
        <!--在收回和展开前面添加的内容的字体颜色-->
        <attr name="ep_end_color" format="color" />
        <!--链接的文字的颜色-->
        <attr name="ep_link_color" format="color" />
        <!--@用户的文字的颜色-->
        <attr name="ep_mention_color" format="color" />
        <!--自定义规则的文字的颜色-->
        <attr name="ep_self_color" format="color" />
        <!--        &lt;!&ndash;链接的图标&ndash;&gt;-->
        <!--        <attr name="ep_link_res" format="reference" />-->
    </declare-styleable>
    <declare-styleable name="ViewStubPro">
        <attr name="inflatedId" format="reference" />
        <attr name="layout" format="reference" />
    </declare-styleable>
    <!-- 带虚线下划线的textview   -->
    <declare-styleable name="DashedTextView">
        <!-- 虚线的颜色       -->
        <attr name="dashedColor" format="color" />
        <!--  虚线线条宽度      -->
        <attr name="dashedStrokeWidth" format="float" />
    </declare-styleable>


    <!-- 带虚线下划线的的textview   -->
    <declare-styleable name="DashLineTextView">
        <!-- 虚线的颜色 -->
        <attr name="dlDashColor" format="color" />
        <!-- 虚线线条宽度 -->
        <attr name="dlDashStrokeWidth" format="dimension" />
        <!-- 虚线长度 -->
        <attr name="dlDashLength" format="dimension" />
        <!-- 虚线间隔 -->
        <attr name="dlDashGap" format="dimension" />
        <!-- 虚线距离对齐线的偏移量（根据对齐方式不同，对齐线含义不同） -->
        <attr name="dlDashOffset" format="dimension" />
        <!-- 虚线对齐方式：baseline, descent, bottom -->
        <attr name="dlDashAlignment" format="enum">
            <enum name="baseline" value="0" />
            <enum name="descent" value="1" />
            <enum name="bottom" value="2" />
        </attr>
        <!-- 是否启用虚线 -->
        <attr name="dlDashEnabled" format="boolean" />
        <!-- 虚线端点样式：round, square -->
        <attr name="dlDashCap" format="enum">
            <enum name="round" value="0" />
            <enum name="square" value="1" />
        </attr>
    </declare-styleable>

    <!-- 登录注册模块输入手机号布局 -->
    <declare-styleable name="LoginInputMobileView">
        <!-- 是否可输入 -->
        <attr name="imv_canInput" format="boolean|reference" />
        <!-- 提示文字 -->
        <attr name="imv_hintText" format="string|reference" />
        <!-- 是否可选择国家区号 -->
        <attr name="imv_canSelectCode" format="boolean|reference" />
    </declare-styleable>
    <!-- 登录注册模块输入密码布局 -->
    <declare-styleable name="LoginInputPwdView">
        <!-- 提示文字 -->
        <attr name="ipv_hintText" format="string|reference" />
    </declare-styleable>
    <!-- 登录注册模块输入内容布局 -->
    <declare-styleable name="LoginInputContentView">
        <!-- 提示文字 -->
        <attr name="icv_hintText" format="string|reference" />
        <!-- 是否输入的是邮箱 -->
        <attr name="icv_isInputEmail" format="boolean|reference" />
    </declare-styleable>
    <!--认证中心页面 开户限制view -->
    <declare-styleable name="KycAuthLimitView">
        <!-- 右边资金顶部title -->
        <attr name="number_title" format="reference|string" />
        <!-- 入金金额 -->
        <attr name="deposit_number" format="reference|string" />
        <!-- 入金金额下的描述 -->
        <attr name="deposit_number_desc" format="reference|string" />
        <!-- 出金金额 -->
        <attr name="withdrawal_number" format="reference|string" />
        <!-- 出金金额下的描述 -->
        <attr name="withdrawal_number_desc" format="reference|string" />
    </declare-styleable>
    <!--认证中心页面 权限解锁需求 -->
    <declare-styleable name="KycAuthRequirementsView">
        <!-- 需求 -->
        <attr name="requirements" format="reference|string" />
    </declare-styleable>
    <declare-styleable name="SwitchButton">
        <attr name="switch_start_color" format="reference|color" />
        <attr name="switch_end_color" format="reference|color" />
        <attr name="switch_border_width" format="reference|dimension" />
        <attr name="switch_can_loading" format="boolean" />
        <attr name="switch_auto_change" format="boolean" />
        <attr name="switch_state" format="enum">
            <enum name="state_true" value="0" />
            <enum name="state_false" value="1" />
            <enum name="state_loading" value="2" />
        </attr>
        <attr name="switch_ring_color" format="reference|color" />
        <attr name="switch_progress_color" format="reference|color" />
    </declare-styleable>
</resources>